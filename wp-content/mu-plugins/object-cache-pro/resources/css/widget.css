
#objectcache_overview .inside,
#dashboard_objectcache .inside {
    padding: 0;
}

#dashboard_objectcache .handle-settings {
    color: #787c82;
    width: 36px;
    height: 36px;
    margin: 0;
    padding: 0;
    border: 0;
    background: none;
    cursor: pointer;
}

#dashboard_objectcache .handle-settings .settings-indicator::before {
    position: relative;
    top: 0.13rem;
    width: 20px;
    height: 20px;
    color: inherit;
    content: "\f108";
    display: inline-block;
    font: normal 20px/1 dashicons;
    text-decoration: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.objectcache\:overview-widget {
    padding: 0 12px;
}

.objectcache\:overview-widget:after {
    content: '';
    display: table;
    clear: both;
}

.objectcache\:overview-widget ul {
    display: inline-block;
    width: 100%;
    margin: 0;
    min-height: 3rem;
}

.objectcache\:overview-widget li {
    float: left;
    width: 50%;
    margin-bottom: 10px;
}

.objectcache\:overview-widget li .dashicons {
    color: #606a73;
}

.objectcache\:overview-widget code {
    font-size: 12px;
    line-height: 20px;
}

.objectcache\:overview-widget .actions .button {
    margin-right: 8px;
    margin-bottom: 12px;
}

.objectcache\:overview-widget .actions .button-ml-auto {
    margin-left: auto;
    margin-right: 0;
}

.objectcache\:widget .actions {
    display: flex;
    flex-wrap: wrap;
}

.objectcache\:widget .actions .button[disabled] {
    cursor: not-allowed;
}

.objectcache\:widget .success {
    color: #008a20;
}

.objectcache\:widget .error {
    color: #d54e21;
}

.objectcache\:widget .alert {
    color: #c21d36;
}

.objectcache\:widget .warning {
    color: #e1a948;
}

.objectcache\:widget .error:last-child,
.objectcache\:widget .alert:last-child,
.objectcache\:widget .warning:last-child {
    margin-bottom: 0;
}

.objectcache\:widget-sub {
    color: #555d66;
    background: #f5f5f5;
    border-top: 1px solid #eee;
    padding: 10px 12px;
}

.objectcache\:widget-sub p,
.objectcache\:widget-sub ul {
    margin: 0;
}

.objectcache\:widget-sub p + ul {
    margin-top: 6px;
}

.objectcache\:widget-sub code {
    margin: 0;
    line-height: 21px;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
}

.objectcache\:widget-charts {
    background: #f5f5f5;
}

.objectcache\:widget-charts .objectcache\:chart {
    border-top: 1px solid #eee;
    cursor: pointer;
}

.objectcache\:chart .apexcharts-tooltip {
    top: 0 !important;
    background: rgba(254, 255, 228, .75) !important;
    box-shadow: none;
    border-radius: 0;
    border: none !important;
    padding: 6px;
    font-size: 12px;
}

.objectcache\:chart .apexcharts-tooltip-marker {
    display: inline-block;
    margin-right: 2px;
    width: 8px;
    height: 8px;
}
