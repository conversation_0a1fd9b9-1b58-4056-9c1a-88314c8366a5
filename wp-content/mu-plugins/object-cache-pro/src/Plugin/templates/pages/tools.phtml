<?php

$screen = get_current_screen();

?>

<div class="wrap">

    <h1>Tools</h1>

    <div id="dashboard-widgets-wrap">

        <div id="dashboard-widgets" class="metabox-holder">
            <div id="postbox-container-1" class="postbox-container">
                <?php do_meta_boxes($screen->id, 'normal', ''); ?>
            </div>
            <div id="postbox-container-2" class="postbox-container">
                <?php do_meta_boxes($screen->id, 'side', ''); ?>
            </div>
            <div id="postbox-container-3" class="postbox-container">
                <?php do_meta_boxes($screen->id, 'column3', ''); ?>
            </div>
            <div id="postbox-container-4" class="postbox-container">
                <?php do_meta_boxes($screen->id, 'column4', ''); ?>
            </div>
        </div>

        <?php wp_nonce_field('closedpostboxes', 'closedpostboxesnonce', false); ?>
        <?php wp_nonce_field('meta-box-order', 'meta-box-order-nonce', false); ?>

    </div>

</div>

<script>
    jQuery(document).ready(function ($) {
        $('.if-js-closed').removeClass('if-js-closed').addClass('closed');

        if (typeof postboxes !== 'undefined') {
            postboxes.add_postbox_toggles('<?php echo $screen->id; ?>');
        }
    });
</script>
