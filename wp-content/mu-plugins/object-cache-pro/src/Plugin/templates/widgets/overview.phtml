<?php

$license = $this->license();
$diagnostics = $this->diagnostics();

$usingRelayCache = $diagnostics->usingRelayCache();

$status = $diagnostics['general']['status']->html;
$dropin = $diagnostics['general']['dropin']->html;
$evictionPolicy = $diagnostics['general']['eviction-policy'];

$redisKeys = $diagnostics['statistics']['redis-keys'];
$redisMemory = $diagnostics['statistics']['redis-memory'];

$relayKeys = $diagnostics['statistics']['relay-keys'] ?? false;
$relayMemory = $diagnostics['statistics']['relay-memory'] ?? false;
$relayMaxMemory = $diagnostics['relay']['relay-maxmemory'] ?? 100;
$relayLicense = $diagnostics['relay']['relay-license'] ?? null;

$configError = $this->config->initException ?? false;
$configDefined = defined('\WP_REDIS_CONFIG');
$configDefinedTooLate = $this->lazyAssConfig();
$networkFlushing = is_multisite() && $this->config->network_flush !== $this->config::NETWORK_FLUSH_ALL;
$isDisabled = $diagnostics->isDisabled();
$dropinExists = $diagnostics->dropinExists();
$dropinIsValid = $dropinExists && $diagnostics->dropinIsValid();
$dropinIsUpToDate = $dropinExists && $diagnostics->dropinIsUpToDate();
$fileModsAllowed = wp_is_file_mod_allowed('object_cache_dropin');

$settingsCapability = is_multisite() ? 'manage_network_options' : $this->screenCapability();
$widgetSettingsLink = current_user_can($settingsCapability) ? network_admin_url($this->baseurl) : false;
$onDashboard = get_current_screen()->id !== $this->screenId;

$adminNonceUrl = function ($action) use ($onDashboard) {
    $function = is_network_admin() ? 'network_admin_url' : 'admin_url';

    $url = $function(
        add_query_arg(
            $onDashboard ? 'objectcache-action' : 'action',
            $action,
            $onDashboard ? 'index.php' : $this->baseurl
        )
    );

    return wp_nonce_url($url, $action);
};

?>

<div class="objectcache:widget objectcache:overview-widget">

    <ul>
        <li>
            <span class="dashicons dashicons-performance"></span>
            Status: <?php echo $status; ?>
        </li>

        <li>
            <span class="dashicons dashicons-admin-plugins"></span>
            Drop-in: <?php echo $dropin; ?>
        </li>

        <?php if ($this->config->shared && $redisKeys->hasValue()) : ?>
            <li>
                <span class="dashicons dashicons-database"></span>
                Cache: <?php printf(_n('%s object', '%s objects', $redisKeys->value), number_format($redisKeys->value)); ?>
            </li>
        <?php elseif ($redisMemory->hasValue()): ?>
            <li title="<?php if ($redisKeys->hasValue()) : printf(_n('%s object', '%s objects', $redisKeys->value), number_format($redisKeys->value)); endif; ?>">
                <span class="dashicons dashicons-database"></span>
                Cache: <?php echo $redisMemory->html; ?>
            </li>
        <?php endif; ?>

        <?php if ($usingRelayCache) : ?>
            <?php $relayAtCapacity = $relayMemory->isWarning() ? "Relay is running at maximum memory capacity ({$relayMaxMemory}) and will cache no further data." : null; ?>

            <?php if ($this->config->shared && $relayKeys->hasValue()) : ?>
                <li title="<?php echo $relayAtCapacity; ?>">
                    <span class="dashicons dashicons-database-add"></span>
                    Relay: <?php printf(_n('%s object', '%s objects', $relayKeys->value), number_format($relayKeys->value)); ?>
                </li>
            <?php elseif ($relayMemory->hasValue()): ?>
                <?php $relayObjects = $relayKeys->hasValue() ? sprintf(_n('%s object', '%s objects', $relayKeys->value), number_format($relayKeys->value)) : null; ?>

                <li title="<?php echo $relayAtCapacity ?: $relayObjects; ?>">
                    <span class="dashicons dashicons-database-add"></span>
                    Relay: <?php echo $relayMemory->html; ?>
                </li>
            <?php endif; ?>
        <?php endif; ?>

        <?php if (! $license->isValid()) : ?>
            <li>
                <span class="dashicons dashicons-admin-network"></span>
                License:
                <data class="error">
                    <?php if (! $this->token()) : ?>
                        Missing token
                    <?php else : ?>
                        <?php echo ucwords($license->state() ?? 'error'); ?>
                    <?php endif; ?>
                </data>
            </li>
        <?php endif; ?>

        <?php if ($usingRelayCache && $relayLicense->hasIssue()) : ?>
            <li title="Relay license">
                <span class="dashicons dashicons-admin-network"></span>
                Relay: <?php echo ucwords($relayLicense->html() ?? 'error'); ?>
            </li>
        <?php endif; ?>

        <?php if ($evictionPolicy->hasValue() && $evictionPolicy->hasIssue()) : ?>
            <li>
                <span class="dashicons dashicons-admin-settings"></span>
                Eviction: <?php echo $evictionPolicy->html; ?>
            </li>
        <?php endif; ?>
    </ul>

    <div class="actions">

        <?php if (! $isDisabled && $dropinExists && $dropinIsValid && $diagnostics->ping()) : ?>
            <?php $flushText = is_network_admin() ? 'Flush Network Cache' : ($networkFlushing ? 'Flush Site Cache' : 'Flush Cache'); ?>
            <?php $flushAction = is_network_admin() ? 'flush-network-cache' : ($networkFlushing ? 'flush-site-cache' : 'flush-cache'); ?>

            <?php if ($license->isValid()) : ?>
                <a class="button button-primary"
                    href="<?php echo $adminNonceUrl($flushAction); ?>"
                >
                    <?php echo $flushText; ?>
                </a>
            <?php else : ?>
                <a class="button button-primary" href="#!" disabled>
                    <?php echo $flushText; ?>
                </a>
            <?php endif; ?>
        <?php endif; ?>

        <?php if ($fileModsAllowed && (! is_multisite() || is_network_admin())) : ?>

            <?php if (! $dropinExists || ! $dropinIsValid) : ?>
                <?php if ($configDefined && ! $configDefinedTooLate && $license->isValid()) : ?>
                    <a class="button button-primary"
                        href="<?php echo $adminNonceUrl('enable-dropin'); ?>"
                    >
                        Enable Cache
                    </a>
                <?php else : ?>
                    <a class="button button-primary" href="#!" disabled>
                        Enable Cache
                    </a>
                <?php endif; ?>
            <?php elseif ($dropinIsValid && ! $dropinIsUpToDate) : ?>
                <a class="button button-secondary"
                    href="<?php echo $adminNonceUrl('update-dropin'); ?>"
                >
                    Update Drop-in
                </a>
            <?php endif; ?>

            <?php if ($dropinExists && $dropinIsValid && ! $isDisabled) : ?>
                <a class="button button-secondary button-ml-auto"
                    onclick="return confirm('Are you sure you want to disable the object cache?')"
                    href="<?php echo $adminNonceUrl('disable-dropin'); ?>"
                >
                    Disable
                </a>
            <?php endif; ?>

        <?php endif; ?>

    </div>

</div>

<?php if ($isDisabled) : ?>

    <div class="objectcache:widget-sub">

        <?php if ($diagnostics->isDisabledUsingConstant()) : ?>

            <p>
                The object cache is disabled using the <code>WP_REDIS_DISABLED</code> constant.
            </p>

        <?php elseif ($diagnostics->isDisabledUsingEnvVar()) : ?>

            <p>
                The object cache is currently disabled using the <code>WP_REDIS_DISABLED</code> environment variable.
            </p>

        <?php endif; ?>

    </div>

<?php elseif (! $configDefined) : ?>

    <div class="objectcache:widget-sub">
        <p>
            To enable the object cache, set up the <code>WP_REDIS_CONFIG</code> constant.
        </p>
    </div>

<?php elseif ($configDefined && $configDefinedTooLate) : ?>

    <div class="objectcache:widget-sub">
        <p>
            The <code>WP_REDIS_CONFIG</code> constant was defined too late. Try moving it to the top of the configuration file.
        </p>
    </div>

<?php elseif ($configError) : ?>

    <div class="objectcache:widget-sub">
        <p>
            An error occurred during the instantiation of the configuration:
        </p>
        <ul>
            <li><code><?php echo esc_html($configError->getMessage()); ?></code></li>
        </ul>
    </div>

<?php elseif (! empty($wp_object_cache_errors)) : ?>

    <div class="objectcache:widget-sub">

        <p>
            <?php if (count($wp_object_cache_errors) > 1) : ?>
                <?php echo count($wp_object_cache_errors); ?> object cache errors have occurred:
            <?php else : ?>
                An object cache error has occurred:
            <?php endif; ?>
        </p>

        <ul>
            <?php foreach (array_slice($wp_object_cache_errors, 0, 3) as $error) : ?>
                <li><code><?php echo esc_html($error); ?></code></li>
            <?php endforeach; ?>
        </ul>

    </div>

<?php elseif (! $this->token()) : ?>

    <div class="objectcache:widget-sub">
        <p>
            Automatic updates for Object Cache Pro are disabled, because no license token is set.
        </p>
    </div>

<?php elseif (! $license->isValid()) : ?>

    <div class="objectcache:widget-sub">
        <p>
            Automatic updates for Object Cache Pro are disabled, because the license token could not be verified or is not valid.
        </p>
    </div>

<?php elseif ($onDashboard && $dropinIsValid && $diagnostics->ping()) : ?>

    <div class="objectcache:widget-charts">

        <?php foreach ($this->widgetCharts() as $id => $metric) : ?>

            <?php $metricGroup = $metric['group'] === 'wp' ? '' : "{$metric['group']}: "; ?>

            <div
                class="objectcache:chart"
                data-chart="<?php echo esc_attr($id); ?>"
                data-type="<?php echo esc_attr(json_encode($metric['type'])); ?>"
                data-title="<?php echo esc_attr(ucfirst($metricGroup) . $metric['title']); ?>"
                data-href="<?php echo esc_url($widgetSettingsLink); ?>"
                data-minimal="1"
            ></div>

        <?php endforeach; ?>

    </div>

<?php endif; ?>

<script>
    if (history.replaceState) {
        if (location.href.indexOf('objectcache-status') > -1) {
            history.replaceState({}, document.title, location.href.replace(/\?objectcache-status=[\w-]+$/, ''));
        }

        if (location.href.indexOf('page=objectcache') > -1) {
            history.replaceState({}, document.title, location.href.replace(/&status=[\w-]+/, ''));
        }
    }
</script>

<?php if ($widgetSettingsLink) : ?>
    <script>
        (function ($) {
            var $widget = $('#dashboard_objectcache');

            if (! $widget.length) {
                return;
            }

            $widget.find('.handle-actions').prepend(
                '<button class="handle-settings" data-href="<?php echo esc_url($widgetSettingsLink); ?>">' +
                '    <span class="screen-reader-text">Go to Settings</span>' +
                '    <span class="settings-indicator" aria-hidden="true"></span>' +
                '</button>'
            );

            $widget.find('.handle-settings').on('click', function (event) {
                event.preventDefault();
                window.location.href = $widget.find('.handle-actions .handle-settings').data('href');
            })
        })(jQuery);
    </script>
<?php endif; ?>
