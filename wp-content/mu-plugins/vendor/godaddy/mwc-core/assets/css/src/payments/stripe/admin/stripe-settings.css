.mwc-payments-stripe-settings-title .wc-admin-breadcrumb {
	margin-right: 1em;
}

.native-stripe-fees {
	font-size: 14px;
}

mark.mwc-payments-stripe-status {
	font-family: 'gdsherpa';
	padding: 4px 12px;
	border-radius: 15px;
	background: #A6FFF8;
	color: #004249;
	font-size: 14px;
	font-weight: 300;
	white-space: nowrap;
}
mark.mwc-payments-stripe-status.connected {
	background: #9FFFB8;
	color: #003A15;
}

mark.mwc-payments-stripe-status.pending {
	background: #FFEEA9;
	color: #111111;
	cursor: inherit;
}

mark.mwc-payments-stripe-status.disconnected {
	background: #CFD4D6;
	color: #111111;
}
.native-stripe-fees span {
	margin-left: 2px;
	text-decoration: none;
}

.mwc-payments-stripe-connect__button {
	border-color: #8995A9;
}

.mwc-stripe-payment_methods_title {
	margin-top: 3.625em;
}

.mwc-stripe-checkout_settings_title {
	margin-top: 3.625em;
}
