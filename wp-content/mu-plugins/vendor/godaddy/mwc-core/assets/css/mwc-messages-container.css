#mwc-messages-container > div:not(:empty) {
	margin: 50px 20px 0 2px;
}
.toplevel_page_mwc-get-help #mwc-messages-container > div:not(:empty) {
	margin-bottom: 24px;
}
.woocommerce-page #mwc-messages-container > div:not(:empty) {
	margin: 100px 40px -60px 40px;
}
.woocommerce-embed-page #mwc-messages-container > div:not(:empty) {
	margin: 100px 20px -70px 20px;
}

/* Rules for WooCommerce 5.2+ */
.mwc-wc-version-gte-5-2.woocommerce-page
#mwc-messages-container
> div:not(:empty) {
	margin-top: var(--mwc-space-7);
	margin-bottom: var(--mwc-space-0);
}
.mwc-wc-version-gte-5-2.woocommerce-embed-page
#mwc-messages-container
> div:not(:empty) {
	margin-top: var(--mwc-space-8);
	margin-bottom: var(--mwc-space-0);
}

/* Rules for GoDaddy Payments Recommendation Notice */

.mwc-godaddy-payments-recommendation.notice {
	display: flex;
	align-items: center;
	padding: var(--mwc-space-1);
	font-family: var(--mwc-font-sans);
	max-width: 960px;
	border-left: 1px solid #c3c4c7;
	border-radius: var(--mwc-radius-md);
}

.mwc-godaddy-payments-recommendation * {
	font-family: "gdsherpa", sans-serif;
}

.mwc-godaddy-payments-recommendation img {
	margin-right: var(--mwc-space-4);
	width: 27px;
	vertical-align: middle;
}

.mwc-godaddy-payments-recommendation h3 {
	min-width: 145px;
	font-size: var(--mwc-font-size-4);
}

.mwc-godaddy-payments-recommendation p {
	margin-left: var(--mwc-space-4);
	font-size: var(--mwc-font-size-3);
	line-height: var(--mwc-line-height-6);
}

.mwc-godaddy-payments-recommendation .mwc-button {
	margin-right: var(--mwc-space-4);
}

.mwc-godaddy-payments-recommendation .notice-dismiss {
	padding: var(--mwc-space-0);
	top: calc(50% - 10px);
	right: 1em;
}

.mwc-godaddy-payments-recommendation .notice-dismiss::before {
	font: inherit;
	content: "";
	background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTExLjgzMzcgMS4zMzM2NkwxMC42NjcgMC4xNjY5OTJMNi4wMDAzMyA0LjgzMzY2TDEuMzMzNjYgMC4xNjY5OTJMMC4xNjY5OTIgMS4zMzM2Nkw0LjgzMzY2IDYuMDAwMzNMMC4xNjY5OTIgMTAuNjY3TDEuMzMzNjYgMTEuODMzN0w2LjAwMDMzIDcuMTY2OTlMMTAuNjY3IDExLjgzMzdMMTEuODMzNyAxMC42NjdMNy4xNjY5OSA2LjAwMDMzTDExLjgzMzcgMS4zMzM2NloiIGZpbGw9IiMxMTExMTEiLz4KPC9zdmc+");
	background-repeat: no-repeat;
	background-position: center center;
}

.mwc-button, .mwc-button:hover, .mwc-button:focus {
	white-space: nowrap;
	text-decoration: none;
	color: #FFF;
}


.mwc-pill {
	background-color: #db1802;
	border-radius: 15px;
	font-size: 11px;
	margin: 1px 0 -1px 5px;
	padding: 5px 8px;
	text-align: center;
	vertical-align: top;
}

.mwc-pill-content {
	color: #fff;
	font-weight: 400;
	vertical-align: top;
}

#mwc-virtual-terminal-notice-meta-box {
	border: none;
	box-shadow: none;
	background: none;
}

#mwc-virtual-terminal-notice-meta-box .postbox-header {
	display: none;
}

#mwc-virtual-terminal-notice-meta-box .inside {
	padding: 0px;
}
